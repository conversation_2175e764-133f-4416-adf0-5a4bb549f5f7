.custom-tab-container {
  height: 44px;
  background: linear-gradient(
      rgba(60, 110, 240, 0.05),
      rgba(60, 110, 240, 0.05)
    ),
    linear-gradient(rgba(241, 242, 244, 1), rgba(241, 242, 244, 1));
  border: 2px solid rgba(255, 255, 255, 1);
  border-radius: 12px 12px 0 0;
  display: flex;

  .custom-tab-text {
    &.first {
      line-height: 44px;
      padding-left: 10px;
      margin-right: 10px;

      &.selected {
        font-size: 14px;
        font-family: PingFang SC;
        font-weight: 600;
        color: rgba(35, 37, 43, 1);
        cursor: pointer;
        position: relative;
        background: linear-gradient(
          180deg,
          rgba(239, 241, 248, 1) 0%,
          rgba(255, 255, 255, 1) 100%
        );

        &::after {
          background: url('@/assets/image/common/tab-right.png') no-repeat 0 /
            contain;
          content: '';
          display: block;
          position: absolute;
          right: -48px;
          top: -3px;
          width: 48px;
          height: 48px;
          z-index: 0;
          pointer-events: none;
        }
      }

      &.unselected {
        cursor: pointer;
        font-size: 14px;
        font-family: PingFang SC;
        font-weight: 600;
        color: rgba(134, 141, 159, 1);
      }
    }

    &.non-first {
      line-height: 44px;
      margin-left: 30px;

      &.selected {
        font-size: 14px;
        font-family: PingFang SC;
        font-weight: 600;
        color: rgba(35, 37, 43, 1);
        cursor: pointer;
        position: relative;
        background: linear-gradient(
          180deg,
          rgba(239, 241, 248, 1) 0%,
          rgba(255, 255, 255, 1) 100%
        );

        &::before {
          background: url('@/assets/image/common/tab-left.png') no-repeat 0 /
            contain;
          content: '';
          display: block;
          position: absolute;
          left: -48px;
          top: -3px;
          z-index: 0;
          pointer-events: none;
          width: 48px;
          height: 48px;
        }

        &::after {
          background: url('@/assets/image/common/tab-right.png') no-repeat 0 /
            contain;
          content: '';
          display: block;
          position: absolute;
          right: -48px;
          top: -3px;
          width: 48px;
          height: 48px;
          z-index: 0;
          pointer-events: none;
        }
      }

      &.unselected {
        cursor: pointer;
        font-size: 14px;
        font-family: PingFang SC;
        font-weight: 600;
        color: rgba(134, 141, 159, 1);
      }
    }
  }
}
