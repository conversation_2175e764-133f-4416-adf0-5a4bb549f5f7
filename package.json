{"name": "operation-platform-ui", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"analyze": "source-map-explorer build/js/*.js --no-border-checks", "start": "env-cmd -f .env.dev webpack server --config ./scripts/config.js", "start-test1": "env-cmd -f .env.test1 webpack server --config ./scripts/config.js", "start-online": "env-cmd -f .env.prod webpack server --config ./scripts/config.js", "build-online": "env-cmd -f .env.prod webpack --config ./scripts/config.js", "build-dev": "env-cmd -f .env.dev webpack --config ./scripts/config.js", "build-test1": "env-cmd -f .env.test1 webpack --config ./scripts/config.js", "test": "echo \"Error: no test specified\" && exit 1", "lint": "eslint --ext .tsx --ext .ts src/ --fix"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@babel/core": "^7.18.9", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-decorators": "^7.18.10", "@babel/plugin-proposal-private-methods": "^7.18.6", "@babel/plugin-proposal-private-property-in-object": "^7.18.6", "@babel/plugin-transform-arrow-functions": "^7.18.6", "@babel/plugin-transform-regenerator": "^7.18.6", "@babel/plugin-transform-runtime": "^7.18.10", "@babel/preset-env": "^7.18.9", "@babel/preset-react": "^7.18.6", "@babel/preset-typescript": "^7.18.6", "@babel/runtime": "^7.18.9", "@babel/runtime-corejs2": "^7.18.9", "@commitlint/config-conventional": "^18.4.3", "@types/lodash": "^4.14.186", "@types/ol": "^6.5.3", "@types/react": "^18.2.62", "@types/react-dom": "^18.3.0", "@types/react-redux": "^7.1.24", "@types/react-router-dom": "^5.3.3", "@types/webpack-env": "^1.18.4", "@typescript-eslint/eslint-plugin": "^5.30.7", "@typescript-eslint/parser": "^5.30.7", "babel-loader": "^8.2.5", "babel-plugin-lodash": "^3.3.4", "clean-webpack-plugin": "^4.0.0", "commitizen": "^4.3.0", "commitlint": "^17.7.1", "copy-webpack-plugin": "^12.0.2", "css-loader": "^6.7.1", "css-minimizer-webpack-plugin": "6.0.0", "cz-customizable": "^7.0.0", "eslint": "^8.20.0", "eslint-html-reporter": "^0.7.4", "eslint-plugin-react": "^7.30.1", "eslint-webpack-plugin": "^3.2.0", "file-loader": "^6.2.0", "hard-source-webpack-plugin": "^0.13.1", "html-webpack-plugin": "5.6.0", "husky": "^8.0.1", "less-loader": "^11.0.0", "lint-staged": "^13.0.3", "mini-css-extract-plugin": "^2.6.1", "postcss-loader": "^7.0.1", "sass": "^1.77.4", "sass-loader": "^14.2.1", "source-map-explorer": "^2.5.2", "style-loader": "^3.3.1", "terser-webpack-plugin": "^5.3.10", "ts-loader": "^9.3.1", "typescript": "4.7.4", "url-loader": "^4.1.1", "webpack": "5.88.2", "webpack-cli": "5.0.1", "webpack-dev-server": "5.0.2", "webpack-manifest-plugin": "5.0.0"}, "dependencies": {"@ant-design/icons": "5.5.1", "@ant-design/pro-table": "^3.15.1", "@jd/x-coreui": "^1.1.69", "@reduxjs/toolkit": "1.8.5", "@turf/turf": "^7.2.0", "antd": "5.17.2", "array-move": "^4.0.0", "babel-polyfill": "^6.26.0", "core-js": "3.36.0", "dayjs": "1.11.11 ", "env-cmd": "^10.1.0", "moment": "^2.30.1", "ol": "^10.2.1", "qiankun": "^2.10.16", "react": "^18.2.0", "react-dom": "^18.2.0", "react-json-view": "^1.21.3", "react-redux": "^8.0.2", "react-router-dom": "^6.3.0", "react-sortable-hoc": "^2.0.0", "redux": "^4.2.0", "redux-saga": "^1.1.3", "sass": "^1.77.6"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"./**/*.{ts,tsx}": "eslint"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "config": {"commitizen": {"path": "./node_modules/cz-customizable"}}}