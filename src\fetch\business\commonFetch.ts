import { request } from '@/fetch/core';
import { parseResponseBody } from '@/utils/utils';

export enum downLoadUrlType {
  CARD = 2,
  VEHICLE = 1,
}
export class CommonApi {
  getStationDepartment(params: {
    stationProductType?: 'vehicle' | 'robot' | 'integrate';
    cityIdList?: any[];
    stationUseCaseList?: any[];
    stationType?: string;
    enable?: any;
    companyNumber?: string;
  }) {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/common/get_cascade_station_address_list',
      body: params,
    };
    return request(options);
  }

  getCityDepartment() {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/common/get_state_city_address_list',
    };
    return request(options);
  }

  getCommonDropDown({ keyList }: { keyList: Array<string> }) {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/common/common_get_down_list',
      body: { keyList },
    };
    return request(options);
  }

  fetchERP() {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/common/common_get_erp_list',
    };
    return request(options);
  }

  getDownloadURL(type: downLoadUrlType) {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/upload/get_download_url',
      body: {
        type,
      },
    };
    return request(options);
  }

  async upload(requestType, params: { bucketName: string; fileKey: string }) {
    let uploadUrl = '';
    if (requestType == 'VEHICLE_BATCH_ADD') {
      uploadUrl = '/k2/management/upload/device_batch_add';
    } else if (requestType == 'VEHICLE_CARD_NO_BATCH_ADD') {
      uploadUrl = '/k2/management/upload/device_card_no_batch_add';
    }
    const options: RequestOptions = {
      method: 'POST',
      path: uploadUrl,
      body: params,
    };
    return request(options);
  }
}
