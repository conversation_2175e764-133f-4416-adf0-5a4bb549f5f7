import React from 'react';
import './index.scss';

interface TabItem {
  key: string;
  label: React.ReactNode;
}

interface CustomTabsProps {
  items: TabItem[];
  activeKey?: string;
  onChange?: (key: string) => void;
}

const CustomTabs: React.FC<CustomTabsProps> = ({
  items,
  activeKey,
  onChange,
}) => {
  const currentActiveKey = activeKey || (items.length > 0 ? items[0].key : '');

  const handleTabClick = (key: string) => {
    if (onChange) {
      onChange(key);
    }
  };

  return (
    <div className="custom-tab-container">
      {items.map((item, index) => {
        const isSelected = currentActiveKey === item.key;
        const isFirst = index === 0;
        return (
          <div
            key={item.key}
            className={`custom-tab-text ${isFirst ? 'first' : 'non-first'} ${
              isSelected ? 'selected' : 'unselected'
            }`}
            onClick={() => handleTabClick(item.key)}
          >
            {item.label}
          </div>
        );
      })}
    </div>
  );
};

export default React.memo(CustomTabs);
