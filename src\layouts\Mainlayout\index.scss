.main-layout-content {
  // min-width: 1600px;
  overflow-x: auto;
  .main-head-content {
    height: 60px;
    width: 100%;
    background-color: #38444e;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    box-sizing: border-box;
  }

  .basic-user-content {
    display: flex;
    flex-direction: row;
    margin-right: 15px;
    font-size: 12px;
    align-items: center;
    justify-content: space-between;

    .username {
      margin-right: 10px;
    }

    .avatar {
      margin-right: 10px;
      width: 30px;
      height: 30px;
    }

    .log-out-btn {
      cursor: pointer;
      margin-right: 10px;
      width: 50px;
      text-align: center;
      font-weight: 500;
      font-size: 15px;
      border-width: 0;
      color: white;
      background-color: rgba($color: #000000, $alpha: 0);
    }
  }

  .main-body-content {
    width: 100%;
    height: calc(100vh - 60px);
    display: flex;
    background-color: #efefef;
    .main-menu-content {
      width: 250px;
      height: 100%;
      background-color: white;
      z-index: 100;
      overflow-y: scroll;
      .menu-content {
        .#{$ant-prefix}-menu {
          .#{$ant-prefix}-menu-submenu {
            .#{$ant-prefix}-menu {
              .#{$ant-prefix}-menu-item {
                &::after {
                  left: 0;
                  border-left: 3px solid #3c6ef0;
                  border-right: none;
                }
              }
              .#{$ant-prefix}-menu-item-selected {
                background-color: #ebf0fd;
                &::after {
                }
              }
            }
          }
        }
      }
    }

    .menu-wrap {
      position: relative;
      .menu-btn {
        width: 12px;
        height: 42px;
        position: absolute;
        right: -12px;
        top: 50%;
        z-index: 9999;
        cursor: pointer;
      }
    }

    .main {
      // width: calc(100vw - 204px);
      width: 100%;
      padding: 0px 12px 16px 12px;
      overflow-y: auto;
    }
  }
}
